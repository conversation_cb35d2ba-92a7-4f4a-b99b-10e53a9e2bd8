import numpy as np
import matplotlib.pyplot as plt
from scipy.fft import fft, fftfreq

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'PingFang SC', 'SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 步骤 1: 读取数据
def load_data(file_path):
    with open(file_path, 'r') as f:
        data = f.readlines()
    # 提取每行逗号前的数值
    signal = [float(line.split(',')[0]) for line in data]
    return np.array(signal)

# 步骤 2: FFT 分析
def analyze_signal(signal, fs):
    N = len(signal)  # 信号长度
    y_fft = fft(signal)  # 快速傅里叶变换
    freqs = fftfreq(N, 1/fs)[:N//2]  # 频率轴（只取正频率部分）
    amplitudes = np.abs(y_fft)[:N//2] * 2 / N  # 振幅归一化
    
    return freqs, amplitudes

# 王唯工理论的正常人谐波振幅比参考值
NORMAL_HARMONIC_RATIOS = [
    100.000,    # H0 基波
    80.388,     # H1
    53.671,     # H2
    41.030,     # H3
    22.520,     # H4
    17.644,     # H5
    12.482,     # H6
    6.949,      # H7
    3.963,      # H8
    2.626,      # H9
    1.791,      # H10
    1.183,      # H11
    0.812       # H12
]

# 步骤 3: 找到基频（心率频率）
def find_fundamental_frequency(freqs, amplitudes):
    """
    找到脉搏波的基频（心率频率）
    通常在0.5-3Hz范围内
    """
    # 限制搜索范围在0.5-3Hz（对应30-180 bpm）
    search_range = (freqs >= 0.5) & (freqs <= 3.0)
    search_freqs = freqs[search_range]
    search_amps = amplitudes[search_range]

    if len(search_amps) == 0:
        return 1.0  # 默认值

    # 找到该范围内振幅最大的频率作为基频
    max_idx = np.argmax(search_amps)
    fundamental_freq = search_freqs[max_idx]

    print(f"检测到的基频（心率频率）: {fundamental_freq:.3f} Hz ({fundamental_freq*60:.1f} bpm)")

    return fundamental_freq

# 步骤 3: 提取谐波振幅并计算相对百分比
def extract_harmonics(freqs, amplitudes, fs, num_harmonics=13):
    # 首先找到基频
    fundamental_freq = find_fundamental_frequency(freqs, amplitudes)

    harmonics = []
    harmonic_freqs = []

    # 计算H0-H12的谐波
    for i in range(num_harmonics):
        if i == 0:
            # H0: 根据王唯工理论，H0是基频的振幅，作为100%基准
            target_freq = fundamental_freq
        else:
            # H1, H2, H3... 是基频的1倍、2倍、3倍...
            target_freq = fundamental_freq * i

        # 找到最接近目标频率的索引
        idx = np.argmin(np.abs(freqs - target_freq))
        harmonics.append(amplitudes[idx])
        harmonic_freqs.append(freqs[idx])

        print(f"H{i}: 目标频率 {target_freq:.3f} Hz, 实际频率 {freqs[idx]:.3f} Hz, 振幅 {amplitudes[idx]:.2f}")

    harmonics = np.array(harmonics)

    # 根据王唯工理论，H0作为基准，设为100%
    # H1-H12相对于H0的百分比
    if harmonics[0] > 0:  # H0作为基准
        relative_amplitude = (harmonics / harmonics[0]) * 100
    else:
        relative_amplitude = np.zeros(len(harmonics))
        relative_amplitude[0] = 100  # H0设为100%

    print(f"H0作为基准: 振幅 {harmonics[0]:.2f}, 相对振幅比 100.00%")

    return harmonics, relative_amplitude, harmonic_freqs, fundamental_freq

# 步骤 3.5: 分析谐波是否符合正常范围
def analyze_harmonic_health(relative_amplitude):
    """
    根据王唯工理论分析谐波健康状况
    正常范围：每个谐波振幅比不超过正常值的正负一倍
    """
    health_status = []
    abnormal_count = 0

    print("\n=== 谐波健康分析 (基于王唯工理论) ===")
    print("谐波 | 实测值(%) | 正常值(%) | 正常范围(%) | 状态")
    print("-" * 60)

    for i in range(min(len(relative_amplitude), len(NORMAL_HARMONIC_RATIOS))):
        measured = relative_amplitude[i]
        normal = NORMAL_HARMONIC_RATIOS[i]

        # 计算正常范围 (正负一倍，但不能低于0)
        if i == 0:  # 基波始终是100%
            lower_bound = 100.0
            upper_bound = 100.0
            status = "正常"
        else:
            lower_bound = max(0, normal - normal)  # 最低为0
            upper_bound = normal + normal  # 最高为正常值的两倍

            if lower_bound <= measured <= upper_bound:
                status = "正常"
            elif measured > upper_bound:
                status = "偏高"
                abnormal_count += 1
            else:
                status = "偏低"
                abnormal_count += 1

        health_status.append(status)
        print(f" H{i:2} | {measured:8.3f} | {normal:8.3f} | {lower_bound:4.1f}-{upper_bound:5.1f} | {status}")

    print("-" * 60)
    total_harmonics = min(len(relative_amplitude), len(NORMAL_HARMONIC_RATIOS))
    normal_count = total_harmonics - abnormal_count
    health_percentage = (normal_count / total_harmonics) * 100

    print(f"总体评估: {normal_count}/{total_harmonics} 个谐波正常 ({health_percentage:.1f}%)")

    # 分析整体谐波强度
    avg_measured = np.mean(relative_amplitude[1:])  # 排除基波
    avg_normal = np.mean(NORMAL_HARMONIC_RATIOS[1:len(relative_amplitude)])
    intensity_ratio = avg_measured / avg_normal

    print(f"谐波强度分析:")
    print(f"  - 平均谐波强度 (实测): {avg_measured:.3f}%")
    print(f"  - 平均谐波强度 (正常): {avg_normal:.3f}%")
    print(f"  - 强度比值: {intensity_ratio:.3f} (正常范围: 0.5-2.0)")

    if intensity_ratio < 0.1:
        intensity_status = "谐波强度过低，可能存在气血不足"
    elif intensity_ratio < 0.5:
        intensity_status = "谐波强度偏低，建议调理"
    elif intensity_ratio > 2.0:
        intensity_status = "谐波强度偏高，可能存在气血瘀滞"
    else:
        intensity_status = "谐波强度正常"

    print(f"  - 强度评估: {intensity_status}")

    if health_percentage >= 80 and 0.5 <= intensity_ratio <= 2.0:
        overall_status = "健康"
    elif health_percentage >= 60 or 0.3 <= intensity_ratio <= 3.0:
        overall_status = "亚健康"
    else:
        overall_status = "需要关注"

    print(f"健康状态: {overall_status}")
    print("=" * 60)

    return health_status, overall_status

# 步骤 4: 绘制频谱图并标注谐波信息
def plot_spectrum_with_harmonics(freqs, amplitudes, harmonics, relative_amplitude, num_harmonics=13):
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))

    # 上图：完整频谱
    ax1.bar(freqs, amplitudes, width=0.1, align='center', alpha=0.7, color='lightblue', label='频谱')

    # 标注谐波点
    for i in range(num_harmonics):
        idx = np.argmin(np.abs(freqs - i))
        harmonic_freq = freqs[idx]
        harmonic_amp = harmonics[i]

        # 在谐波位置绘制红色标记点
        ax1.plot(harmonic_freq, harmonic_amp, 'ro', markersize=6, label='谐波点' if i == 0 else "")

        # 添加简单的谐波编号标注
        if harmonic_amp > max(amplitudes) * 0.005:
            ax1.annotate(f'H{i}',
                        xy=(harmonic_freq, harmonic_amp),
                        xytext=(harmonic_freq, harmonic_amp + max(amplitudes) * 0.05),
                        ha='center', va='bottom', fontsize=8,
                        bbox=dict(boxstyle='round,pad=0.2', facecolor='yellow', alpha=0.8))

    ax1.set_xlabel('频率 (Hz)', fontsize=12)
    ax1.set_ylabel('振幅', fontsize=12)
    ax1.set_title('脉搏波 FFT 分析结果 - 完整频谱', fontsize=14)
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    ax1.set_xlim(0, 50)

    # 下图：低频部分详细视图
    ax2.bar(freqs, amplitudes, width=0.1, align='center', alpha=0.7, color='lightgreen', label='低频频谱')

    # 详细标注前12个谐波
    for i in range(num_harmonics):
        idx = np.argmin(np.abs(freqs - i))
        harmonic_freq = freqs[idx]
        harmonic_amp = harmonics[i]

        # 绘制谐波点
        ax2.plot(harmonic_freq, harmonic_amp, 'ro', markersize=8)

        # 详细标注信息
        ax2.annotate(f'H{i}\n振幅: {harmonic_amp:.1f}\n比例: {relative_amplitude[i]:.2f}%',
                    xy=(harmonic_freq, harmonic_amp),
                    xytext=(harmonic_freq + 0.5, harmonic_amp + max(amplitudes[:int(len(amplitudes)*0.1)]) * 0.1),
                    ha='left', va='bottom', fontsize=9,
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='lightcyan', alpha=0.9),
                    arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0.1', color='red'))

    ax2.set_xlabel('频率 (Hz)', fontsize=12)
    ax2.set_ylabel('振幅', fontsize=12)
    ax2.set_title('脉搏波 FFT 分析结果 - 低频详细视图 (含谐波数据)', fontsize=14)
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    ax2.set_xlim(0, 15)

    plt.tight_layout()

    # 保存图片而不是显示，避免阻塞
    plt.savefig('pulse_wave_fft_analysis.png', dpi=300, bbox_inches='tight')
    print("频谱图已保存为 'pulse_wave_fft_analysis.png'")
    plt.show()

# 步骤 5: 绘制谐波对比图
def plot_harmonic_comparison(relative_amplitude):
    """绘制实测谐波与正常值的对比图"""
    harmonics_count = min(len(relative_amplitude), len(NORMAL_HARMONIC_RATIOS))
    x = np.arange(harmonics_count)

    measured_values = relative_amplitude[:harmonics_count]
    normal_values = NORMAL_HARMONIC_RATIOS[:harmonics_count]

    plt.figure(figsize=(14, 8))

    # 绘制柱状图对比
    width = 0.35
    plt.bar(x - width/2, measured_values, width, label='实测值', color='lightcoral', alpha=0.8)
    plt.bar(x + width/2, normal_values, width, label='正常值 (王唯工理论)', color='lightgreen', alpha=0.8)

    # 添加数值标注
    for i in range(harmonics_count):
        plt.text(i - width/2, measured_values[i] + max(measured_values) * 0.01,
                f'{measured_values[i]:.1f}', ha='center', va='bottom', fontsize=9)
        plt.text(i + width/2, normal_values[i] + max(normal_values) * 0.01,
                f'{normal_values[i]:.1f}', ha='center', va='bottom', fontsize=9)

    plt.xlabel('谐波编号', fontsize=12)
    plt.ylabel('振幅比 (%)', fontsize=12)
    plt.title('谐波振幅比对比分析 (实测值 vs 正常值)', fontsize=14)
    plt.xticks(x, [f'H{i}' for i in range(harmonics_count)])
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('harmonic_comparison.png', dpi=300, bbox_inches='tight')
    print("谐波对比图已保存为 'harmonic_comparison.png'")
    plt.show()

# 步骤 6: 绘制以正常值为基准线的偏差分析图
def plot_harmonic_deviation(relative_amplitude):
    """绘制以正常值为基准线0的偏差分析图"""
    harmonics_count = min(len(relative_amplitude), len(NORMAL_HARMONIC_RATIOS))
    x = np.arange(harmonics_count)

    measured_values = relative_amplitude[:harmonics_count]
    normal_values = NORMAL_HARMONIC_RATIOS[:harmonics_count]

    # 计算偏差值（实测值 - 正常值）
    deviations = measured_values - normal_values

    plt.figure(figsize=(15, 10))

    # 创建两个子图
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12))

    # 上图：偏差柱状图
    colors = ['red' if dev < 0 else 'green' for dev in deviations]
    bars = ax1.bar(x, deviations, color=colors, alpha=0.7, edgecolor='black', linewidth=1)

    # 添加基准线（y=0）
    ax1.axhline(y=0, color='blue', linestyle='-', linewidth=2, label='正常值基准线')

    # 添加数值标注
    for i, (bar, dev) in enumerate(zip(bars, deviations)):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + (0.5 if height >= 0 else -1.5),
                f'{dev:+.1f}', ha='center', va='bottom' if height >= 0 else 'top',
                fontsize=10, fontweight='bold')

    ax1.set_xlabel('谐波编号', fontsize=12)
    ax1.set_ylabel('偏差值 (%)', fontsize=12)
    ax1.set_title('谐波偏差分析 (以王唯工理论正常值为基准线)', fontsize=14)
    ax1.set_xticks(x)
    ax1.set_xticklabels([f'H{i}' for i in range(harmonics_count)])
    ax1.grid(True, alpha=0.3)
    ax1.legend()

    # 添加颜色说明
    ax1.text(0.02, 0.98, '红色: 低于正常值\n绿色: 高于正常值',
             transform=ax1.transAxes, fontsize=11, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    # 下图：详细数值对比表格式图
    ax2.axis('tight')
    ax2.axis('off')

    # 创建表格数据
    table_data = []
    table_data.append(['谐波', '实测值(%)', '正常值(%)', '偏差值(%)', '偏差率(%)', '状态'])

    for i in range(harmonics_count):
        deviation_rate = (deviations[i] / normal_values[i] * 100) if normal_values[i] != 0 else 0
        status = '偏低' if deviations[i] < 0 else '偏高' if deviations[i] > 0 else '正常'

        table_data.append([
            f'H{i}',
            f'{measured_values[i]:.2f}',
            f'{normal_values[i]:.2f}',
            f'{deviations[i]:+.2f}',
            f'{deviation_rate:+.1f}',
            status
        ])

    # 创建表格
    table = ax2.table(cellText=table_data[1:], colLabels=table_data[0],
                     cellLoc='center', loc='center', bbox=[0, 0, 1, 1])

    # 设置表格样式
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1, 2)

    # 根据偏差值设置行颜色
    for i in range(1, len(table_data)):
        deviation = deviations[i-1]
        if deviation < 0:
            color = 'lightcoral'  # 红色系
        elif deviation > 0:
            color = 'lightgreen'  # 绿色系
        else:
            color = 'lightgray'   # 灰色

        for j in range(len(table_data[0])):
            table[(i, j)].set_facecolor(color)

    # 设置标题行样式
    for j in range(len(table_data[0])):
        table[(0, j)].set_facecolor('lightblue')
        table[(0, j)].set_text_props(weight='bold')

    ax2.set_title('详细偏差数据表', fontsize=14, pad=20)

    plt.tight_layout()
    plt.savefig('harmonic_deviation_analysis.png', dpi=300, bbox_inches='tight')
    print("谐波偏差分析图已保存为 'harmonic_deviation_analysis.png'")
    plt.show()

# 主函数
def main():
    # 参数设置
    file_path = '采集数据/chl2.txt'  # 数据文件路径
    fs = 200  # 采样频率 (Hz)
    num_harmonics = 13  # 提取的谐波数量 (增加到13个)

    # 读取数据
    signal = load_data(file_path)

    # FFT 分析
    freqs, amplitudes = analyze_signal(signal, fs)

    # 提取谐波振幅并计算相对百分比
    harmonics, relative_amplitude, harmonic_freqs, fundamental_freq = extract_harmonics(freqs, amplitudes, fs, num_harmonics)

    # 输出基本结果
    print("=== 谐波分析结果 ===")
    print("谐波编号 | 振幅       | 振幅比 (%)")
    print("-------------------------------------")
    for i in range(num_harmonics):
        print(f"{i:8} | {harmonics[i]:8.4f} | {relative_amplitude[i]:8.4f}")

    # 进行健康分析
    health_status, overall_status = analyze_harmonic_health(relative_amplitude)

    # 绘制频谱图并标注谐波信息
    # plot_spectrum_with_harmonics(freqs, amplitudes, harmonics, relative_amplitude, num_harmonics)

    # 绘制谐波对比图
    # plot_harmonic_comparison(relative_amplitude)

    # 绘制谐波偏差分析图
    plot_harmonic_deviation(relative_amplitude)

# 运行主函数
if __name__ == "__main__":
    main()