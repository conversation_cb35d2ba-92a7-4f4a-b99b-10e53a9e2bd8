import numpy as np
import matplotlib.pyplot as plt
from scipy.fft import fft, fftfreq

# 步骤 1: 读取数据
def load_data(file_path):
    with open(file_path, 'r') as f:
        data = f.readlines()
    # 提取每行逗号前的第一个数值
    signal = [float(line.split(',')[0]) for line in data]
    return np.array(signal)

# 步骤 2: FFT 分析
def analyze_signal(signal, fs):
    N = len(signal)  # 信号长度
    y_fft = fft(signal)  # 快速傅里叶变换
    freqs = fftfreq(N, 1/fs)[:N//2]  # 频率轴（只取正频率部分）
    amplitudes = np.abs(y_fft)[:N//2] * 2 / N  # 振幅归一化
    
    return freqs, amplitudes

# 步骤 3: 提取谐波振幅并计算相对百分比
def extract_harmonics(freqs, amplitudes, fs, num_harmonics=12):
    harmonics = []
    for i in range(num_harmonics):
        # 找到最接近 i Hz 的索引
        idx = np.argmin(np.abs(freqs - i))
        harmonics.append(amplitudes[idx])
    
    harmonics = np.array(harmonics)
    relative_amplitude = (harmonics / harmonics[0]) * 100  # 相对振幅百分比
    
    return harmonics, relative_amplitude

# 步骤 4: 绘制频谱图
def plot_spectrum(freqs, amplitudes):
    plt.figure(figsize=(12, 6))
    plt.bar(freqs, amplitudes, width=0.1, align='center')
    plt.xlabel('Frequency (Hz)')
    plt.ylabel('Amplitude')
    plt.title('Pulse Wave FFT Analysis Result')
    plt.grid(True)
    plt.show()

# 主函数
def main():
    # 参数设置
    file_path = '采集数据/chl2.txt'  # 数据文件路径
    fs = 200  # 采样频率 (Hz)
    num_harmonics = 12  # 提取的谐波数量
    
    # 读取数据
    signal = load_data(file_path)
    
    # FFT 分析
    freqs, amplitudes = analyze_signal(signal, fs)
    
    # 提取谐波振幅并计算相对百分比
    harmonics, relative_amplitude = extract_harmonics(freqs, amplitudes, fs, num_harmonics)
    
    # 输出结果
    print("谐波编号 | 振幅       | 振幅比 (%)")
    print("-------------------------------------")
    for i in range(num_harmonics):
        print(f"{i:8} | {harmonics[i]:8.4f} | {relative_amplitude[i]:8.4f}")
    
    # 绘制频谱图
    plot_spectrum(freqs, amplitudes)

# 运行主函数
if __name__ == "__main__":
    main()