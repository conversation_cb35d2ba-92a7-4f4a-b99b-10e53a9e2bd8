import numpy as np
import matplotlib.pyplot as plt
from scipy.fft import fft, fftfreq
from scipy.signal import butter, filtfilt
import pandas as pd

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'PingFang SC', 'SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 1. 读取数据（只取逗号前的数值）
def load_data(file_path):
    with open(file_path, 'r') as f:
        data = []
        for line in f:
            if ',' in line:
                value = float(line.strip().split(',')[0])
                data.append(value)
    return np.array(data)

# 2. 从中段80%内随机截取20秒数据（默认采样率 fs=200Hz）
def crop_center(signal, fs=200, duration=20):
    total_samples = len(signal)
    duration_samples = fs * duration

    # 计算中段80%的范围
    center_80_start = int(total_samples * 0.1)  # 从10%位置开始
    center_80_end = int(total_samples * 0.9)    # 到90%位置结束
    center_80_length = center_80_end - center_80_start

    # 在中段80%范围内随机截取20秒
    if center_80_length >= duration_samples:
        # 在80%中段范围内随机选择起始位置
        max_start_offset = center_80_length - duration_samples
        random_offset = np.random.randint(0, max_start_offset + 1)
        start_index = center_80_start + random_offset
        end_index = start_index + duration_samples

        print(f"数据总长度: {total_samples/fs:.1f}秒")
        print(f"中段80%范围: {center_80_start/fs:.1f}秒 - {center_80_end/fs:.1f}秒")
        print(f"随机截取位置: {start_index/fs:.1f}秒 - {end_index/fs:.1f}秒")
    else:
        # 如果80%中段长度不足20秒，则使用全部80%中段数据
        start_index = center_80_start
        end_index = center_80_end
        print(f"警告：80%中段数据长度不足20秒，使用全部中段数据 ({center_80_length/fs:.1f}秒)")

    return signal[start_index:end_index]

# 3. 设计并应用低通滤波器
def low_pass_filter(signal, cutoff=10, fs=200, order=5):
    nyquist = 0.5 * fs
    normal_cutoff = cutoff / nyquist
    b, a = butter(order, normal_cutoff, btype='low', analog=False)
    filtered_signal = filtfilt(b, a, signal)
    return filtered_signal

# 4. 进行 FFT 分析
def analyze_fft(signal, fs=200):
    N = len(signal)
    yf = fft(signal)
    xf = fftfreq(N, 1/fs)[:N//2]
    amplitude = (2.0/N) * np.abs(yf[:N//2])
    return xf, amplitude

# 5. 提取特定频率附近的能量
def extract_energy(xf, amp, target_freqs):
    energies = []
    for freq in target_freqs:
        idx = np.argmin(np.abs(xf - freq))
        energies.append(amp[idx])
    return np.array(energies)

# 6. 经络—频率映射表（王唯工理论参考）
def get_meridian_mapping():
    # 按照指定顺序：心包经、肝经、肾经、脾经、肺经、胃经、胆经、膀胱经、大肠经、三焦经、小肠经、心经
    meridians = [
        "心包经", "肝经", "肾经", "脾经", "肺经", "胃经",
        "胆经", "膀胱经", "大肠经", "三焦经", "小肠经", "心经"
    ]
    frequencies = [1.0, 3.1, 2.9, 6.4, 9.5, 7.9, 2.4, 3.6, 8.7, 1.7, 4.3, 5.3]
    return frequencies, meridians

# 7. 主程序
def main():
    file_path = '采集数据/fwb1.txt'  # 替换为你自己的文件名
    fs = 200  # 采样率
    duration = 20  # 截取时长（秒）

    # 步骤一：读取原始数据
    raw_signal = load_data(file_path)

    # 步骤二：截取中间 20 秒数据
    signal = crop_center(raw_signal, fs=fs, duration=duration)

    # 步骤三：应用低通滤波器（截止频率为 10 Hz）
    filtered_signal = low_pass_filter(signal, cutoff=10, fs=fs)

    # 步骤四：FFT 分析
    xf, amp = analyze_fft(filtered_signal, fs)

    # 步骤五：获取经络频率映射
    frequencies, meridians = get_meridian_mapping()

    # 步骤六：提取各频率对应的能量
    energies = extract_energy(xf, amp, frequencies)

    # 步骤七：计算相对振幅比（以心包经为基准）
    base_energy = energies[8]  # 心包经 = 1.0 Hz (索引8)
    relative_energy = (energies / base_energy) * 100

    # 步骤八：构建 DataFrame 表格
    result_df = pd.DataFrame({
        "经络名称": meridians,
        "对应频率(Hz)": frequencies,
        "振幅": energies,
        "振幅比(%)": relative_energy
    })

    print("=== 各经络对应的能量分布 ===")
    print(result_df.to_string(index=False))

    # 步骤九：绘制原始信号与频谱图
    t = np.arange(len(filtered_signal)) / fs

    plt.figure(figsize=(12, 6))

    plt.subplot(2, 1, 1)
    plt.plot(t, filtered_signal)
    plt.title('滤波后的脉搏波形')
    plt.xlabel('时间 (s)')
    plt.ylabel('振幅')
    plt.grid(True)

    plt.subplot(2, 1, 2)
    plt.bar(xf, amp, width=0.1, label='频谱', color='skyblue')
    for freq in frequencies:
        plt.axvline(freq, color='red', linestyle='--', linewidth=1)
    plt.xlim(0, 10)
    plt.title('滤波后频谱图（FFT）')
    plt.xlabel('频率 (Hz)')
    plt.ylabel('振幅')
    plt.grid(True)
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    main()