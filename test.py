import numpy as np
import matplotlib.pyplot as plt
from scipy.fft import fft, fftfreq
import matplotlib.font_manager as fm

# 设置中文字体 - 按优先级尝试不同的中文字体
chinese_fonts = ['Arial Unicode MS', 'PingFang SC', 'SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['font.sans-serif'] = chinese_fonts  # 支持中文显示
plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号

# 步骤 1: 读取数据
def load_data(file_path):
    with open(file_path, 'r') as f:
        lines = f.readlines()

    # 处理逗号分隔的数据，取第一列作为信号数据
    signal_data = []
    for line in lines:
        line = line.strip()
        if line:  # 跳过空行
            # 分割逗号分隔的数据，取第一个值
            values = line.split(',')
            if len(values) >= 1:
                try:
                    signal_data.append(float(values[0]))
                except ValueError:
                    continue  # 跳过无法转换的行

    # 转换为numpy数组
    signal = np.array(signal_data)

    # 打印数据处理信息
    print(f"数据文件读取完成:")
    print(f"  - 总行数: {len(signal_data)}")
    print(f"  - 前5个数据点: {signal_data[:5]}")
    print(f"  - 数据范围: {min(signal_data):.2f} ~ {max(signal_data):.2f}")
    print()

    return signal

# 步骤 2: FFT 分析
def analyze_signal(signal, fs):
    N = len(signal)  # 信号长度
    y_fft = fft(signal)  # 快速傅里叶变换
    freqs = fftfreq(N, 1/fs)[:N//2]  # 频率轴（只取正频率部分）
    amplitudes = np.abs(y_fft)[:N//2] * 2 / N  # 振幅归一化
    
    return freqs, amplitudes

# 步骤 3: 提取谐波振幅并计算相对百分比
def extract_harmonics(freqs, amplitudes, fs, num_harmonics=12):
    harmonics = []
    for i in range(num_harmonics):
        # 找到最接近 i * fs 的频率索引
        idx = np.argmin(np.abs(freqs - i))
        harmonics.append(amplitudes[idx])
    
    harmonics = np.array(harmonics)
    relative_amplitude = (harmonics / harmonics[0]) * 100  # 相对振幅百分比
    
    return harmonics, relative_amplitude

# 步骤 4: 输出结果
def print_table(harmonics, relative_amplitude):
    print("谐波编号 | 振幅       | 振幅比 (%)")
    print("-------------------------------------")
    for i in range(len(harmonics)):
        print(f"{i:8} | {harmonics[i]:8.4f} | {relative_amplitude[i]:8.4f}")

# 步骤 5: 绘制频谱图
def plot_spectrum(freqs, amplitudes):
    plt.figure(figsize=(10, 6))
    plt.bar(freqs, amplitudes, width=0.1)
    plt.xlabel('频率 (Hz)')
    plt.ylabel('振幅')
    plt.title('脉搏波 FFT 分析结果')
    plt.grid(True)
    plt.show()

# 主函数
def main():
    # 参数设置
    file_path = 'chl.txt'  # 数据文件路径
    fs = 200  # 采样频率 (Hz)
    num_harmonics = 12  # 提取的谐波数量
    
    # 读取数据
    signal = load_data(file_path)
    
    # FFT 分析
    freqs, amplitudes = analyze_signal(signal, fs)
    
    # 提取谐波振幅并计算相对百分比
    harmonics, relative_amplitude = extract_harmonics(freqs, amplitudes, fs, num_harmonics)
    
    # 输出表格
    print_table(harmonics, relative_amplitude)
    
    # 绘制频谱图
    plot_spectrum(freqs, amplitudes)

# 运行主函数
if __name__ == "__main__":
    main()