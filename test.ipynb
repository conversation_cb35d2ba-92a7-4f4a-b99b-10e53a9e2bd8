{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 脉搏波 FFT 分析\n", "\n", "本笔记本用于分析脉搏波信号的频谱特征，提取谐波振幅并计算相对百分比。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 导入必要的库"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from scipy.fft import fft, fftfreq\n", "import matplotlib.font_manager as fm\n", "\n", "# 设置中文字体 - 按优先级尝试不同的中文字体\n", "chinese_fonts = ['Arial Unicode MS', 'PingFang SC', 'SimHei', 'Microsoft YaHei', 'DejaVu Sans']\n", "plt.rcParams['font.sans-serif'] = chinese_fonts  # 支持中文显示\n", "plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 数据读取函数"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# 步骤 1: 读取数据\n", "def load_data(file_path):\n", "    with open(file_path, 'r') as f:\n", "        lines = f.readlines()\n", "    \n", "    # 处理逗号分隔的数据，取第一列作为信号数据\n", "    signal_data = []\n", "    for line in lines:\n", "        line = line.strip()\n", "        if line:  # 跳过空行\n", "            # 分割逗号分隔的数据，取第一个值\n", "            values = line.split(',')\n", "            if len(values) >= 1:\n", "                try:\n", "                    signal_data.append(float(values[0]))\n", "                except ValueError:\n", "                    continue  # 跳过无法转换的行\n", "    \n", "    # 转换为numpy数组\n", "    signal = np.array(signal_data)\n", "    \n", "    # 打印数据处理信息\n", "    print(f\"数据文件读取完成:\")\n", "    print(f\"  - 总行数: {len(signal_data)}\")\n", "    print(f\"  - 前5个数据点: {signal_data[:5]}\")\n", "    print(f\"  - 数据范围: {min(signal_data):.2f} ~ {max(signal_data):.2f}\")\n", "    print()\n", "    \n", "    return signal"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. FFT 分析函数"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# 步骤 2: FFT 分析\n", "def analyze_signal(signal, fs):\n", "    N = len(signal)  # 信号长度\n", "    y_fft = fft(signal)  # 快速傅里叶变换\n", "    freqs = fftfreq(N, 1/fs)[:N//2]  # 频率轴（只取正频率部分）\n", "    amplitudes = np.abs(y_fft)[:N//2] * 2 / N  # 振幅归一化\n", "    \n", "    return freqs, amplitudes"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 谐波提取函数"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# 步骤 3: 提取谐波振幅并计算相对百分比\n", "def extract_harmonics(freqs, amplitudes, fs, num_harmonics=12):\n", "    harmonics = []\n", "    for i in range(num_harmonics):\n", "        # 找到最接近 i * fs 的频率索引\n", "        idx = np.argmin(np.abs(freqs - i))\n", "        harmonics.append(amplitudes[idx])\n", "    \n", "    harmonics = np.array(harmonics)\n", "    relative_amplitude = (harmonics / harmonics[0]) * 100  # 相对振幅百分比\n", "    \n", "    return harmonics, relative_amplitude"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 结果输出函数"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# 步骤 4: 输出结果\n", "def print_table(harmonics, relative_amplitude):\n", "    print(\"谐波编号 | 振幅       | 振幅比 (%)\")\n", "    print(\"-------------------------------------\")\n", "    for i in range(len(harmonics)):\n", "        print(f\"{i:8} | {harmonics[i]:8.4f} | {relative_amplitude[i]:8.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 频谱图绘制函数"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["# 步骤 5: 绘制频谱图\n", "def plot_spectrum(freqs, amplitudes):\n", "    # plt.figure(figsize=(10, 6))\n", "    plt.bar(freqs, amplitudes, width=0.1)\n", "    plt.xlabel('频率 (Hz)')\n", "    plt.ylabel('振幅')\n", "    plt.title('脉搏波 FFT 分析结果')\n", "    plt.grid(True)\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 参数设置"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["分析参数:\n", "  - 数据文件: chl.txt\n", "  - 采样频率: 200 Hz\n", "  - 谐波数量: 12\n", "\n"]}], "source": ["# 参数设置\n", "file_path = 'chl.txt'  # 数据文件路径\n", "fs = 200  # 采样频率 (Hz)\n", "num_harmonics = 12  # 提取的谐波数量\n", "\n", "print(f\"分析参数:\")\n", "print(f\"  - 数据文件: {file_path}\")\n", "print(f\"  - 采样频率: {fs} Hz\")\n", "print(f\"  - 谐波数量: {num_harmonics}\")\n", "print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. 执行分析"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["数据文件读取完成:\n", "  - 总行数: 12380\n", "  - 前5个数据点: [8214.0, 8208.0, 8204.0, 8197.0, 8192.0]\n", "  - 数据范围: 488.00 ~ 8214.00\n", "\n"]}], "source": ["# 读取数据\n", "signal = load_data(file_path)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["FFT 分析完成，频率范围: 0.00 ~ 99.98 Hz\n"]}], "source": ["# FFT 分析\n", "freqs, amplitudes = analyze_signal(signal, fs)\n", "print(f\"FFT 分析完成，频率范围: {freqs[0]:.2f} ~ {freqs[-1]:.2f} Hz\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["# 提取谐波振幅并计算相对百分比\n", "harmonics, relative_amplitude = extract_harmonics(freqs, amplitudes, fs, num_harmonics)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. 结果展示"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["谐波编号 | 振幅       | 振幅比 (%)\n", "-------------------------------------\n", "       0 | 8415.2118 | 100.0000\n", "       1 |  73.0841 |   0.8685\n", "       2 |   9.5064 |   0.1130\n", "       3 |  31.3291 |   0.3723\n", "       4 |  10.5292 |   0.1251\n", "       5 |  12.3984 |   0.1473\n", "       6 |   2.2592 |   0.0268\n", "       7 |  11.0708 |   0.1316\n", "       8 |   2.0332 |   0.0242\n", "       9 |   7.7042 |   0.0916\n", "      10 |   4.5177 |   0.0537\n", "      11 |   4.2235 |   0.0502\n"]}], "source": ["# 输出表格\n", "print_table(harmonics, relative_amplitude)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 绘制频谱图\n", "plot_spectrum(freqs, amplitudes)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 4}